#!/usr/bin/env python3
"""
训练结果对比工具
用于对比原始版本和增强版本的训练结果
"""
import pandas as pd
import numpy as np
import os
import sys
from pathlib import Path


def load_results(result_file):
    """加载结果文件"""
    if not os.path.exists(result_file):
        return None
    df = pd.read_csv(result_file)
    return df


def load_history(history_file):
    """加载训练历史"""
    if not os.path.exists(history_file):
        return None
    df = pd.read_csv(history_file)
    return df


def print_section(title, width=70):
    """打印分隔线"""
    print("\n" + "=" * width)
    print(f"  {title}")
    print("=" * width)


def compare_results():
    """对比训练结果"""
    
    print_section("📊 训练结果对比工具")
    
    # 文件路径
    original_result = "outputs/result_davis.csv"
    enhanced_result = "outputs_enhanced/result_davis.csv"
    original_history = "outputs/history_davis.csv"
    enhanced_history = "outputs_enhanced/history_davis.csv"
    
    # 加载结果
    print("\n🔍 正在加载结果文件...")
    original_res = load_results(original_result)
    enhanced_res = load_results(enhanced_result)
    original_hist = load_history(original_history)
    enhanced_hist = load_history(enhanced_history)
    
    # ==================== 最终结果对比 ====================
    print_section("🎯 最终结果对比")
    
    if original_res is not None:
        print("\n原始版本 (outputs/result_davis.csv):")
        print("-" * 70)
        for col in original_res.columns:
            value = original_res[col].values[0]
            print(f"  {col:15s}: {value:.6f}")
    else:
        print("\n⚠️  原始版本结果文件不存在")
    
    if enhanced_res is not None:
        print("\n增强版本 (outputs_enhanced/result_davis.csv):")
        print("-" * 70)
        for col in enhanced_res.columns:
            value = enhanced_res[col].values[0]
            print(f"  {col:15s}: {value:.6f}")
    else:
        print("\n⚠️  增强版本结果文件不存在（可能还未训练）")
    
    # 计算改进百分比
    if original_res is not None and enhanced_res is not None:
        print("\n📈 性能改进:")
        print("-" * 70)
        for col in original_res.columns:
            orig_val = original_res[col].values[0]
            enh_val = enhanced_res[col].values[0]
            
            # RMSE, MSE, MAE越小越好
            if col.lower() in ['rmse', 'mse', 'mae']:
                improvement = (orig_val - enh_val) / orig_val * 100
                symbol = "↓" if improvement > 0 else "↑"
                color = "✅" if improvement > 0 else "❌"
            else:  # Pearson, Spearman, CI越大越好
                improvement = (enh_val - orig_val) / orig_val * 100
                symbol = "↑" if improvement > 0 else "↓"
                color = "✅" if improvement > 0 else "❌"
            
            print(f"  {col:15s}: {improvement:+7.2f}% {symbol}  {color}")
    
    # ==================== 训练历史分析 ====================
    print_section("📜 训练历史分析")
    
    if original_hist is not None:
        best_original = original_hist[original_hist['is_best'] == True]
        if len(best_original) > 0:
            last_best = best_original.iloc[-1]
            print(f"\n原始版本最佳结果 (Epoch {int(last_best['epoch'])}):")
            print("-" * 70)
            if 'ci' in last_best:
                print(f"  CI:        {last_best['ci']:.6f}")
            if 'mae' in last_best:
                print(f"  MAE:       {last_best['mae']:.6f}")
            if 'rmse' in last_best:
                print(f"  RMSE:      {last_best['rmse']:.6f}")
            if 'pearson' in last_best:
                print(f"  Pearson:   {last_best['pearson']:.6f}")
            if 'spearman' in last_best:
                print(f"  Spearman:  {last_best['spearman']:.6f}")
            
            total_epochs = len(original_hist)
            print(f"\n  训练轮数:  {total_epochs}")
            print(f"  最佳轮数:  {int(last_best['epoch'])}")
    else:
        print("\n⚠️  原始版本历史文件不存在")
    
    if enhanced_hist is not None:
        best_enhanced = enhanced_hist[enhanced_hist['is_best'] == True]
        if len(best_enhanced) > 0:
            last_best = best_enhanced.iloc[-1]
            print(f"\n增强版本最佳结果 (Epoch {int(last_best['epoch'])}):")
            print("-" * 70)
            if 'ci' in last_best:
                print(f"  CI:        {last_best['ci']:.6f}")
            if 'mae' in last_best:
                print(f"  MAE:       {last_best['mae']:.6f}")
            if 'rmse' in last_best:
                print(f"  RMSE:      {last_best['rmse']:.6f}")
            if 'pearson' in last_best:
                print(f"  Pearson:   {last_best['pearson']:.6f}")
            if 'spearman' in last_best:
                print(f"  Spearman:  {last_best['spearman']:.6f}")
            
            if 'learning_rate' in last_best:
                print(f"  学习率:    {last_best['learning_rate']:.6f}")
            if 'train_loss' in last_best:
                print(f"  训练损失:  {last_best['train_loss']:.6f}")
            
            total_epochs = len(enhanced_hist)
            print(f"\n  训练轮数:  {total_epochs}")
            print(f"  最佳轮数:  {int(last_best['epoch'])}")
    else:
        print("\n⚠️  增强版本历史文件不存在（可能还未训练）")
    
    # ==================== 收敛分析 ====================
    if original_hist is not None or enhanced_hist is not None:
        print_section("🎓 收敛分析")
        
        if original_hist is not None and 'ci' in original_hist.columns:
            ci_values = original_hist['ci'].values
            # 计算最近50个epoch的标准差
            if len(ci_values) >= 50:
                recent_std = np.std(ci_values[-50:])
                print(f"\n原始版本:")
                print(f"  最近50轮CI标准差: {recent_std:.6f}")
                if recent_std < 0.002:
                    print("  状态: ✅ 已收敛")
                elif recent_std < 0.005:
                    print("  状态: ⚠️  接近收敛")
                else:
                    print("  状态: 🔄 仍在优化中")
        
        if enhanced_hist is not None and 'ci' in enhanced_hist.columns:
            ci_values = enhanced_hist['ci'].values
            if len(ci_values) >= 50:
                recent_std = np.std(ci_values[-50:])
                print(f"\n增强版本:")
                print(f"  最近50轮CI标准差: {recent_std:.6f}")
                if recent_std < 0.002:
                    print("  状态: ✅ 已收敛")
                elif recent_std < 0.005:
                    print("  状态: ⚠️  接近收敛")
                else:
                    print("  状态: 🔄 仍在优化中")
    
    # ==================== 建议 ====================
    print_section("💡 优化建议")
    
    if enhanced_res is None:
        print("\n1. 🚀 还未运行增强版本训练")
        print("   建议运行: python train_enhanced_v2.py")
    elif original_res is not None and enhanced_res is not None:
        # 检查是否有显著提升
        orig_ci = original_res['ci'].values[0]
        enh_ci = enhanced_res['ci'].values[0]
        improvement = (enh_ci - orig_ci) / orig_ci * 100
        
        if improvement > 1:
            print("\n✅ 增强版本性能显著提升！")
            print("   建议:")
            print("   - 继续使用增强版本配置")
            print("   - 可以尝试模型集成进一步提升")
            print("   - 考虑在其他数据集上测试")
        elif improvement > 0:
            print("\n⚠️  增强版本有轻微提升")
            print("   建议:")
            print("   - 调整超参数（学习率、dropout等）")
            print("   - 尝试更深的网络结构")
            print("   - 增加训练轮数")
        else:
            print("\n❌ 增强版本未见提升")
            print("   可能原因:")
            print("   - 训练轮数不够（建议至少200轮）")
            print("   - 超参数不适合当前数据集")
            print("   - 模型复杂度过高导致过拟合")
            print("   建议:")
            print("   - 检查训练日志")
            print("   - 调整dropout和weight_decay")
            print("   - 尝试不同的随机种子")
    
    # ==================== 总结 ====================
    print_section("📋 总结")
    
    if original_res is not None and enhanced_res is not None:
        print("\n性能对比总结:")
        print("-" * 70)
        
        metrics = ['ci', 'mae', 'rmse', 'pearson', 'spearman']
        improved = 0
        total = 0
        
        for metric in metrics:
            if metric in original_res.columns and metric in enhanced_res.columns:
                total += 1
                orig_val = original_res[metric].values[0]
                enh_val = enhanced_res[metric].values[0]
                
                if metric.lower() in ['rmse', 'mse', 'mae']:
                    if enh_val < orig_val:
                        improved += 1
                else:
                    if enh_val > orig_val:
                        improved += 1
        
        print(f"\n  改进指标数: {improved}/{total}")
        if improved == total:
            print("  评价: 🎉 全面提升！")
        elif improved > total / 2:
            print("  评价: ✅ 整体提升")
        elif improved > 0:
            print("  评价: ⚠️  部分提升")
        else:
            print("  评价: ❌ 需要调整")
    
    print("\n" + "=" * 70 + "\n")


def show_best_epochs():
    """显示最佳epoch的详细信息"""
    print_section("🏆 最佳Epoch详细信息")
    
    files = {
        '原始版本': 'outputs/history_davis.csv',
        '增强版本': 'outputs_enhanced/history_davis.csv'
    }
    
    for name, file in files.items():
        if os.path.exists(file):
            df = pd.read_csv(file)
            best_epochs = df[df['is_best'] == True]
            
            print(f"\n{name}:")
            print("-" * 70)
            if len(best_epochs) > 0:
                print(f"  最佳epoch总数: {len(best_epochs)}")
                print(f"  首次最佳: Epoch {int(best_epochs.iloc[0]['epoch'])}")
                print(f"  最后最佳: Epoch {int(best_epochs.iloc[-1]['epoch'])}")
                
                # 显示前5个最佳epoch
                print(f"\n  最佳Epochs (最多显示5个):")
                for i, row in best_epochs.head(5).iterrows():
                    print(f"    Epoch {int(row['epoch'])}: CI={row['ci']:.6f}", end="")
                    if 'mae' in row:
                        print(f", MAE={row['mae']:.6f}", end="")
                    print()
            else:
                print("  未找到最佳epoch记录")
        else:
            print(f"\n{name}:")
            print("-" * 70)
            print("  ⚠️  文件不存在")
    
    print("\n" + "=" * 70 + "\n")


if __name__ == '__main__':
    try:
        compare_results()
        
        # 如果用户想看详细信息
        if len(sys.argv) > 1 and sys.argv[1] == '--detailed':
            show_best_epochs()
            
    except Exception as e:
        print(f"\n❌ 错误: {str(e)}")
        print("\n请确保训练结果文件存在:")
        print("  - outputs/result_davis.csv")
        print("  - outputs/history_davis.csv")
        print("  - outputs_enhanced/result_davis.csv (可选)")
        print("  - outputs_enhanced/history_davis.csv (可选)")

