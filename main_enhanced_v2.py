"""
增强版主训练脚本 - 集成多项优化
"""
import sys, os
import torch
import torch.nn as nn
import numpy as np
from torch.utils.data import DataLoader, Dataset
import argparse
from data import CPIDataset
from models.core import *
from utils import *
from sklearn.metrics import precision_recall_curve, roc_curve, auc, f1_score, accuracy_score, roc_auc_score, average_precision_score
import random
import torch.backends.cudnn as cudnn
from sklearn.metrics import auc
from sklearn.metrics import RocCurveDisplay
import csv
from datetime import datetime
import warnings
from data_loader_fix import create_safe_dataloader, setup_multiprocessing
from torch.cuda.amp import autocast, GradScaler

try:
    from rdkit import RDLogger
    RDLogger.DisableLog('rdApp.*')
except Exception:
    pass

warnings.filterwarnings("ignore", category=DeprecationWarning)

os.environ['CUDA_VISIBLE_DEVICES'] = '0,1,2,3'


# ===================== 优化1: 组合损失函数 =====================
class CombinedLoss(nn.Module):
    """组合损失函数: MSE + MAE + Smooth L1"""
    def __init__(self, alpha=0.6, beta=0.25, gamma=0.15):
        super().__init__()
        self.alpha = alpha  # MSE权重
        self.beta = beta    # MAE权重
        self.gamma = gamma  # Smooth L1权重
        
    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        mae_loss = F.l1_loss(pred, target)
        smooth_l1 = F.smooth_l1_loss(pred, target)
        
        total_loss = self.alpha * mse_loss + self.beta * mae_loss + self.gamma * smooth_l1
        return total_loss


# ===================== 优化2: Warmup学习率调度器 =====================
class WarmupCosineScheduler:
    """Warmup + Cosine Annealing学习率调度器"""
    def __init__(self, optimizer, warmup_epochs, total_epochs, min_lr=1e-6):
        self.optimizer = optimizer
        self.warmup_epochs = warmup_epochs
        self.total_epochs = total_epochs
        self.min_lr = min_lr
        self.base_lr = optimizer.param_groups[0]['lr']
        self.current_epoch = 0
        
    def step(self):
        self.current_epoch += 1
        if self.current_epoch <= self.warmup_epochs:
            # Warmup阶段：线性增长
            lr = self.base_lr * (self.current_epoch / self.warmup_epochs)
        else:
            # Cosine Annealing阶段
            progress = (self.current_epoch - self.warmup_epochs) / (self.total_epochs - self.warmup_epochs)
            lr = self.min_lr + (self.base_lr - self.min_lr) * 0.5 * (1 + np.cos(np.pi * progress))
        
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = lr
        
        return lr


# ===================== 优化3: 带混合精度和梯度裁剪的训练函数 =====================
def train_dta_enhanced(model, loss_fn, train_loader, optimizer, scaler, epoch, max_grad_norm=1.0):
    """增强版DTA训练函数"""
    print(f'Training on {len(train_loader.dataset)} samples...')
    if hasattr(torch.cuda, 'empty_cache'):
        torch.cuda.empty_cache()

    model.train()
    total_loss = 0
    num_batches = 0

    for batch_idx, data in enumerate(train_loader):
        try:
            optimizer.zero_grad()
            
            # 混合精度训练
            with autocast():
                output = model(data)
                loss = loss_fn(output, data['LABEL'].view(-1, 1).float().cuda())
            
            # 反向传播
            scaler.scale(loss).backward()
            
            # 梯度裁剪
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=max_grad_norm)
            
            # 优化器步进
            scaler.step(optimizer)
            scaler.update()
            
            total_loss += loss.item()
            num_batches += 1

            # 定期清理GPU内存
            if batch_idx % 10 == 0:
                torch.cuda.empty_cache()

            if batch_idx % 20 == 0:
                avg_loss = total_loss / num_batches
                print(f'Train epoch: {epoch} [{batch_idx * len(data["LABEL"])}/{len(train_loader.dataset)} '
                      f'({100. * batch_idx / len(train_loader):.0f}%)]\t'
                      f'Loss: {loss.item():.6f}\tAvg Loss: {avg_loss:.6f}')
                      
        except torch.cuda.OutOfMemoryError:
            print(f"CUDA out of memory at batch {batch_idx}. Clearing cache and skipping batch.")
            torch.cuda.empty_cache()
            continue
    
    return total_loss / num_batches if num_batches > 0 else 0


def predicting_dta(model, loader):
    """DTA预测函数"""
    model.eval()
    total_preds = torch.Tensor()
    total_labels = torch.Tensor()
    print(f'Make prediction for {len(loader.dataset)} samples...')
    with torch.no_grad():
        for data in loader:
            output = model(data)
            total_preds = torch.cat((total_preds, output.cpu()), 0)
            total_labels = torch.cat((total_labels, data['LABEL'].view(-1, 1).cpu()), 0)
    return total_labels.numpy().flatten(), total_preds.numpy().flatten()


def train_dti_enhanced(model, loss_fn, train_loader, optimizer, scaler, epoch, max_grad_norm=1.0):
    """增强版DTI训练函数"""
    print(f'Training on {len(train_loader.dataset)} samples...')
    if hasattr(torch.cuda, 'empty_cache'):
        torch.cuda.empty_cache()

    model.train()
    total_loss = 0
    num_batches = 0
    
    for batch_idx, data in enumerate(train_loader):
        try:
            optimizer.zero_grad()
            
            # 混合精度训练
            with autocast():
                output = model(data)
                loss = loss_fn(output, data['LABEL'].long().cuda())
            
            # 反向传播
            scaler.scale(loss).backward()
            
            # 梯度裁剪
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=max_grad_norm)
            
            # 优化器步进
            scaler.step(optimizer)
            scaler.update()
            
            total_loss += loss.item()
            num_batches += 1

            # 定期清理GPU内存
            if batch_idx % 10 == 0:
                torch.cuda.empty_cache()

            if batch_idx % 20 == 0:
                avg_loss = total_loss / num_batches
                print(f'Train epoch: {epoch} [{batch_idx * len(data["LABEL"])}/{len(train_loader.dataset)} '
                      f'({100. * batch_idx / len(train_loader):.0f}%)]\t'
                      f'Loss: {loss.item():.6f}\tAvg Loss: {avg_loss:.6f}')
                      
        except torch.cuda.OutOfMemoryError:
            print(f"CUDA out of memory at batch {batch_idx}. Clearing cache and skipping batch.")
            torch.cuda.empty_cache()
            continue
    
    return total_loss / num_batches if num_batches > 0 else 0


def predicting_dti(model, loader):
    """DTI预测函数"""
    model.eval()
    total_preds = []
    total_labels = []

    print(f'Making predictions for {len(loader.dataset)} samples...')
    with torch.no_grad():
        for data in loader:
            output = model(data)
            total_preds.append(output.cpu().numpy())
            total_labels.append(data['LABEL'].long().cpu().numpy())

    return np.concatenate(total_labels), np.concatenate(total_preds)


def davis_dataloader(seed=0, batch_size=256, workers=4, dataset='davis', data_path='./data'):
    """Davis数据集加载器"""
    print(f'\nrunning on {dataset}')

    path = data_path + '/' + dataset
    data = CPIDataset(f'{path}.csv', f'{path}/compound', f'{path}/protein')
    print(f'Total samples: {len(data)}')

    test_size = int(len(data) * 0.1)

    train_dataset, test_dataset = torch.utils.data.random_split(
        dataset=data,
        lengths=[len(data) - (test_size * 2), test_size * 2],
        generator=torch.Generator().manual_seed(seed)
    )
    val_dataset, test_dataset = torch.utils.data.random_split(
        dataset=test_dataset,
        lengths=[test_size, test_size],
        generator=torch.Generator().manual_seed(seed)
    )

    train_loader = create_safe_dataloader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=data.collate_fn,
    )

    val_loader = create_safe_dataloader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        collate_fn=data.collate_fn,
    )

    test_loader = create_safe_dataloader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        collate_fn=data.collate_fn,
    )

    return train_loader, val_loader, test_loader


def others_dataloader(batch_size, workers=4, dataset='davis', data_path='./data'):
    """其他数据集加载器"""
    print(f'\nrunning on {dataset}')

    path = data_path + '/' + dataset

    if dataset == 'bindingdb':
        train_set = CPIDataset(f'{path}_train.csv', f'{path}/compound', f'{path}/protein/train')
        test_set = CPIDataset(f'{path}_test.csv', f'{path}/compound', f'{path}/protein/test')
    else:
        train_set = CPIDataset(f'{path}_train.csv', f'{path}/compound', f'{path}/protein')
        test_set = CPIDataset(f'{path}_test.csv', f'{path}/compound', f'{path}/protein')

    train_loader = create_safe_dataloader(
        train_set,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=train_set.collate_fn,
    )

    test_loader = create_safe_dataloader(
        test_set,
        batch_size=batch_size,
        shuffle=False,
        collate_fn=test_set.collate_fn,
    )

    return train_loader, test_loader


def run(args: argparse.Namespace):
    """主训练函数"""
    # 设置多进程环境
    setup_multiprocessing()

    # 设置CUDA内存管理
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'

    data_path = args.root_data_path
    dataset = args.dataset
    batch_size = args.batch_size
    LR = args.learning_rate
    NUM_EPOCHS = args.max_epochs
    seed = args.seed
    output_dir = getattr(args, 'output_dir', './outputs')
    os.makedirs(output_dir, exist_ok=True)

    model = PMMRNet(args).cuda()

    print('=' * 70)
    print('🔧 训练配置：')
    print(f'  Learning rate: {LR}')
    print(f'  Epochs: {NUM_EPOCHS}')
    print(f'  Batch size: {batch_size}')
    print(f'  Dataset: {dataset}')
    print(f'  Objective: {args.objective}')
    print('=' * 70)

    # 加载数据
    if dataset == 'davis':
        train_loader, val_loader, test_loader = davis_dataloader(seed, batch_size, 4, dataset, data_path)
    else:
        train_loader, test_loader = others_dataloader(batch_size, 4, dataset, data_path)

    if args.objective == 'regression':
        # ===================== 使用组合损失函数 =====================
        loss_fn = CombinedLoss(alpha=0.6, beta=0.25, gamma=0.15)
        
        # ===================== 使用AdamW优化器 + weight decay =====================
        optimizer = torch.optim.AdamW(
            model.parameters(), 
            lr=LR,
            weight_decay=0.01,  # L2正则化
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        # ===================== 使用Warmup + Cosine学习率调度 =====================
        scheduler = WarmupCosineScheduler(
            optimizer, 
            warmup_epochs=10,
            total_epochs=NUM_EPOCHS,
            min_lr=1e-6
        )
        
        # ===================== 混合精度训练Scaler =====================
        scaler = GradScaler()
        
        best_mae = 1000
        best_ci = 0
        best_epoch = -1
        model_file_name = os.path.join(output_dir, f'model_{dataset}.pth')
        result_file_name = os.path.join(output_dir, f'result_{dataset}.csv')
        history_file_name = os.path.join(output_dir, f'history_{dataset}.csv')
        eval_split = 'val' if dataset == 'davis' else 'test'
        
        print(f'\n🏋️  开始训练 (共{NUM_EPOCHS}个epoch)...\n')
        
        for epoch in range(NUM_EPOCHS):
            # 训练一个epoch
            avg_train_loss = train_dta_enhanced(
                model, loss_fn, train_loader, optimizer, scaler, epoch + 1, max_grad_norm=1.0
            )
            
            # 学习率调度
            current_lr = scheduler.step()
            
            # 验证/测试
            if dataset == 'davis':
                G, P = predicting_dta(model, val_loader)
            else:
                G, P = predicting_dta(model, test_loader)
            
            # 计算指标
            ret = [rmse(G, P), mse(G, P), pearson(G, P), spearman(G, P), ci(G, P)]
            current_mae = mae(G, P)
            
            # 判断是否为最佳模型
            is_best = ret[4] > best_ci  # 使用CI作为主要指标
            timestamp = datetime.now().isoformat(timespec='seconds')
            
            # 保存训练历史
            exists_before = os.path.exists(history_file_name) and os.path.getsize(history_file_name) > 0
            with open(history_file_name, 'a', newline='') as f:
                writer = csv.writer(f)
                if not exists_before:
                    writer.writerow(['timestamp', 'epoch', 'dataset', 'objective', 'split', 
                                   'rmse', 'mse', 'mae', 'pearson', 'spearman', 'ci', 
                                   'train_loss', 'learning_rate', 'is_best'])
                writer.writerow([timestamp, epoch + 1, dataset, args.objective, eval_split, 
                               ret[0], ret[1], float(current_mae), ret[2], ret[3], ret[4],
                               avg_train_loss, current_lr, is_best])
            
            # 保存最佳模型
            if is_best:
                torch.save(model.state_dict(), model_file_name)
                with open(result_file_name, 'w', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(['rmse', 'mse', 'mae', 'pearson', 'spearman', 'ci'])
                    writer.writerow([ret[0], ret[1], float(current_mae), ret[2], ret[3], ret[4]])
                best_epoch = epoch + 1
                best_ci = ret[4]
                best_mae = float(current_mae)
                print(f'🎉 Best model updated at epoch {best_epoch}!')
                print(f'   CI: {best_ci:.6f} | MAE: {best_mae:.6f} | RMSE: {ret[0]:.6f}')
                print(f'   Pearson: {ret[2]:.6f} | Spearman: {ret[3]:.6f}')
                print(f'   Model saved to: {model_file_name}')
            else:
                print(f'📊 Epoch {epoch + 1}: CI={ret[4]:.6f} (Best: {best_ci:.6f} @ epoch {best_epoch})')
                print(f'   MAE: {current_mae:.6f} | RMSE: {ret[0]:.6f} | LR: {current_lr:.6f}')
            
            # 早停
            if epoch - best_epoch > 60:
                print(f"\n⏹️  Early stopping triggered (no improvement for 60 epochs)")
                break
        
        print('\n' + '=' * 70)
        print('🏁 训练完成!')
        print(f'📈 最佳结果 (Epoch {best_epoch}):')
        print(f'   CI: {best_ci:.6f}')
        print(f'   MAE: {best_mae:.6f}')
        print('=' * 70)

    else:  # classification
        loss_fn = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(
            model.parameters(), 
            lr=LR,
            weight_decay=0.01,
            betas=(0.9, 0.999)
        )
        scheduler = WarmupCosineScheduler(
            optimizer, 
            warmup_epochs=10,
            total_epochs=NUM_EPOCHS,
            min_lr=1e-6
        )
        scaler = GradScaler()
        
        best_auc = 0
        best_epoch = -1
        model_file_name = os.path.join(output_dir, f'model_{dataset}.pth')
        result_file_name = os.path.join(output_dir, f'result_{dataset}.csv')
        history_file_name = os.path.join(output_dir, f'history_{dataset}.csv')
        eval_split = 'val' if dataset == 'davis' else 'test'
        
        for epoch in range(NUM_EPOCHS):
            avg_train_loss = train_dti_enhanced(
                model, loss_fn, train_loader, optimizer, scaler, epoch + 1, max_grad_norm=1.0
            )
            
            current_lr = scheduler.step()

            if dataset == 'davis':
                G, P = predicting_dti(model, val_loader)
            else:
                G, P = predicting_dti(model, test_loader)

            ret = [roc_auc_score(G, P[:, 1]), average_precision_score(G, P[:, 1]),
                   f1_score(G, np.argmax(P, axis=1)), accuracy_score(G, np.argmax(P, axis=1))]
            
            is_best = ret[0] > best_auc
            timestamp = datetime.now().isoformat(timespec='seconds')
            
            exists_before = os.path.exists(history_file_name) and os.path.getsize(history_file_name) > 0
            with open(history_file_name, 'a', newline='') as f:
                writer = csv.writer(f)
                if not exists_before:
                    writer.writerow(['timestamp', 'epoch', 'dataset', 'objective', 'split', 
                                   'auc', 'aupr', 'f1', 'acc', 'train_loss', 'learning_rate', 'is_best'])
                writer.writerow([timestamp, epoch + 1, dataset, args.objective, eval_split, 
                               ret[0], ret[1], ret[2], ret[3], avg_train_loss, current_lr, is_best])
            
            if is_best:
                torch.save(model.state_dict(), model_file_name)
                with open(result_file_name, 'w', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(['auc', 'aupr', 'f1', 'acc'])
                    writer.writerow(ret)
                best_epoch = epoch + 1
                best_auc = ret[0]
                print(f'🎉 AUC improved at epoch {best_epoch}: {best_auc:.6f}')
            else:
                print(f'📊 Epoch {epoch + 1}: AUC={ret[0]:.6f} (Best: {best_auc:.6f} @ epoch {best_epoch})')
            
            if epoch - best_epoch > 60:
                print("⏹️  Early stopping")
                break


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='PMMR Enhanced Training')
    parser.add_argument('--dataset', type=str, default='davis', help='dataset')
    parser.add_argument('--root_data_path', type=str, default='./data', help='data path')
    parser.add_argument('--output_dir', type=str, default='./outputs', help='directory to save models and results')
    parser.add_argument('--batch_size', type=int, default=256, help='batch size')
    parser.add_argument('--learning_rate', type=float, default=0.0001, help='learning rate')
    parser.add_argument('--max_epochs', type=int, default=1000, help='max epochs')
    parser.add_argument('--num_workers', type=int, default=4, help='num workers')
    parser.add_argument('--seed', type=int, default=0, help='seed')
    parser.add_argument('--objective', type=str, default='regression', help='objective')

    # model parameters
    parser.add_argument('--decoder_layers', type=int, default=3, help='decoder layers')
    parser.add_argument('--linear_heads', type=int, default=10, help='linear heads')
    parser.add_argument('--linear_hidden_dim', type=int, default=32, help='linear hidden dimension')
    parser.add_argument('--decoder_heads', type=int, default=4, help='decoder heads')
    parser.add_argument('--encoder_heads', type=int, default=4, help='encoder heads')
    parser.add_argument('--gnn_layers', type=int, default=3, help='gnn layers')
    parser.add_argument('--encoder_layers', type=int, default=1, help='encoder layers')
    parser.add_argument('--decoder_nums', type=int, default=1, help='decoder nums')
    parser.add_argument('--decoder_dim', type=int, default=128, help='decoder dimension')
    parser.add_argument('--compound_gnn_dim', type=int, default=78, help='compound gnn dimension')
    parser.add_argument('--pf_dim', type=int, default=1024, help='feedforward dimension')
    parser.add_argument('--dropout', type=float, default=0.2, help='dropout')
    parser.add_argument('--protein_dim', type=int, default=128, help='protein dimension')
    parser.add_argument('--compound_structure_dim', type=int, default=78, help='compound structure dimension')
    parser.add_argument('--compound_text_dim', type=int, default=128, help='compound text dimension')
    parser.add_argument('--compound_pretrained_dim', type=int, default=384, help='compound pretrained dimension')
    parser.add_argument('--protein_pretrained_dim', type=int, default=480, help='protein pretrained dimension')

    args = parser.parse_args()
    print("Arguments parsed:", args)

    # Set random seed for reproducibility
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    random.seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(args.seed)
        torch.cuda.manual_seed_all(args.seed)

    print("Starting enhanced training...")
    run(args)

