import math
import torch
from models.mlp_modules import MultiLayerMLP
from models.gnn import MultiGIN
from torch import nn
import torch.nn.functional as F
from models.decoder import DecoderLayer
from torch.nn.utils.weight_norm import weight_norm
import os


os.environ['CUDA_VISIBLE_DEVICES'] = '0,1,2,3'


class LinearAttention(nn.Module):
    def __init__(self, input_dim=128, hidden_dim=32, heads=10):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.heads = heads

        self.linear_first = torch.nn.Linear(self.input_dim, self.hidden_dim)
        self.linear_second = torch.nn.Linear(self.hidden_dim, self.heads)
        self.softmax = nn.Softmax(dim=-1)


    def forward(self, x, masks):
        sentence_att = F.tanh(self.linear_first(x))
        sentence_att = self.linear_second(sentence_att)
        sentence_att = sentence_att.transpose(1, 2)
        minus_inf = -9e15 * torch.ones_like(sentence_att)
        e = torch.where(masks > 0.5, sentence_att, minus_inf)  # (B,heads,seq_len)
        att = self.softmax(e)
        sentence_embed = att @ x
        avg_sentence_embed = torch.sum(sentence_embed, 1) / self.heads

        return avg_sentence_embed



class PMMRNet(torch.nn.Module):
    def __init__(self, args):

        super(PMMRNet, self).__init__()

        self.gnn_dim = args.compound_gnn_dim
        self.dropout = args.dropout
        self.decoder_dim = args.decoder_dim
        self.decoder_heads = args.decoder_heads
        self.compound_text_dim = args.compound_text_dim
        self.compound_structure_dim = args.compound_structure_dim
        self.protein_dim = args.protein_dim
        self.linear_heads = args.linear_heads
        self.linears_hidden_dim = args.linear_hidden_dim
        self.feedforward_dim = args.pf_dim
        self.encoder_heads = args.encoder_heads
        self.encoder_layers = args.encoder_layers
        self.protein_pretrained_dim = args.protein_pretrained_dim
        self.compound_pretrained_dim = args.compound_pretrained_dim
        self.objective = args.objective



        # GIN layers for molecular graph feature extraction
        self.drug_gin = MultiGIN(self.compound_structure_dim, self.gnn_dim)
        
        # Enhanced MLP for graph-sequence coupling in decoder
        # Input: gnn_dim*2 (mean+max pooling) + compound_text_dim*2 (weighted+original)
        self.graph_seq_fusion_mlp = MultiLayerMLP(self.gnn_dim * 2 + self.compound_text_dim * 2, 
                                                  self.decoder_dim, self.dropout)
        
        # Residual projection for fusion
        self.fusion_residual_proj = nn.Linear(self.compound_text_dim, self.decoder_dim)
        self.fusion_ln = nn.LayerNorm(self.decoder_dim)

        self.cross_atten = DecoderLayer(self.decoder_dim, self.decoder_heads, self.dropout)


        self.drug_attn = LinearAttention(self.compound_text_dim, self.linears_hidden_dim, self.linear_heads)
        self.target_attn = LinearAttention(self.protein_dim, self.linears_hidden_dim, self.linear_heads)
        self.inter_attn_one = LinearAttention(self.protein_dim, self.linears_hidden_dim, self.linear_heads)



        self.encoder_layer = nn.TransformerEncoderLayer(d_model=self.compound_text_dim, dim_feedforward=self.feedforward_dim, nhead=self.encoder_heads)
        self.transformer_encoder = nn.TransformerEncoder(self.encoder_layer, num_layers=self.encoder_layers)

        self.encoder_layer2 = nn.TransformerEncoderLayer(d_model=self.protein_dim, dim_feedforward=self.feedforward_dim, nhead=self.encoder_heads)
        self.transformer_encoder2 = nn.TransformerEncoder(self.encoder_layer2, num_layers=self.encoder_layers)


        self.fc1 = nn.Linear(self.gnn_dim, self.compound_text_dim)
        self.fc2 = nn.Linear(self.protein_pretrained_dim,self.protein_dim)
        self.fc3 = nn.Linear(self.compound_pretrained_dim,self.compound_text_dim)

        self.drug_ln = nn.LayerNorm(self.compound_text_dim)
        self.target_ln = nn.LayerNorm(self.protein_dim)


        if self.objective == 'regression':
            self.lin = nn.Sequential(
                nn.Linear(self.protein_dim * 3, 1024),
                nn.BatchNorm1d(1024),
                nn.ReLU(),
                nn.Dropout(self.dropout),

                nn.Linear(1024, 512),
                nn.BatchNorm1d(512),
                nn.ReLU(),
                nn.Dropout(self.dropout),

                nn.Linear(512, 256),
                nn.BatchNorm1d(256),
                nn.ReLU(),
                nn.Dropout(self.dropout * 0.5),  # 最后一层减小dropout

                nn.Linear(256, 1)
            )
        elif self.objective == 'classification':
            self.lin = nn.Sequential(nn.Linear(self.protein_dim * 3, 512), nn.ReLU(), nn.Dropout(self.dropout),
                                    nn.Linear(512, 2))


    def generate_masks(self, adj, adj_sizes, n_heads):
        out = torch.ones(adj.shape[0], adj.shape[1])
        max_size = adj.shape[1]
        if isinstance(adj_sizes, int):
            out[0, adj_sizes:max_size] = 0
        else:
            for e_id, drug_len in enumerate(adj_sizes):
                out[e_id, drug_len: max_size] = 0
        out = out.unsqueeze(1).expand(-1, n_heads, -1)
        return out.to(device=adj.device)

    def make_masks(self, atom_num, protein_num, compound_max_len, protein_max_len):
        batch_size = len(atom_num)
        device = next(self.parameters()).device
        compound_mask = torch.zeros((batch_size, compound_max_len), device=device)
        protein_mask = torch.zeros((batch_size, protein_max_len), device=device)

        for i in range(batch_size):
            compound_mask[i, :atom_num[i]] = 1
            protein_mask[i, :protein_num[i]] = 1
        compound_mask = compound_mask.unsqueeze(1).unsqueeze(2)
        protein_mask = protein_mask.unsqueeze(1).unsqueeze(2)
        return compound_mask, protein_mask

    def forward(self, data):
        # 使用更保守的内存管理策略
        device = next(self.parameters()).device

        # 分批移动数据到GPU以减少内存峰值
        compound_x = data['COMPOUND_NODE_FEAT'].to(device, non_blocking=True)
        compound_adj = data['COMPOUND_ADJ'].to(device, non_blocking=True)
        compound_emb = data['COMPOUND_EMBEDDING'].to(device, non_blocking=True)

        target_emb = data['PROTEIN_EMBEDDING'].to(device, non_blocking=True)
        # target_adj = data['PROTEIN_CONTACT_MAP'].cuda()

        compound_smiles_max_len = data['COMPOUND_EMBEDDING'].shape[1]
        compound_node_max_len = data['COMPOUND_NODE_FEAT'].shape[1]

        node_mask, smiles_mask = self.make_masks(
            data["COMPOUND_NODE_NUM"],
            data["COMPOUND_SMILES_LENGTH"],
            compound_node_max_len,
            compound_smiles_max_len,
        )

        #Drug
        # Step 1: GIN extracts molecular graph features
        compound_graph_feat = self.drug_gin(compound_x, compound_adj)
        # 释放不再需要的变量
        del compound_x, compound_adj

        xd_f1 = self.drug_ln(self.fc1(compound_graph_feat))

        # Step 2: Extract SMILES sequence features
        compound_smiles = self.transformer_encoder(self.fc3(compound_emb))
        # 释放不再需要的变量
        del compound_emb

        xd_f2 = self.drug_ln(compound_smiles)
        del compound_smiles

        # Step 3: Enhanced MLP-based explicit coupling with attention
        # Multi-scale graph pooling (不只是简单平均)
        graph_mean = torch.mean(compound_graph_feat, dim=1, keepdim=True)  # [B, 1, gnn_dim]
        graph_max = torch.max(compound_graph_feat, dim=1, keepdim=True)[0]  # [B, 1, gnn_dim] 
        graph_pooled = torch.cat([graph_mean, graph_max], dim=-1)  # [B, 1, gnn_dim*2]
        graph_pooled = graph_pooled.expand(-1, xd_f2.size(1), -1)  # [B, seq_len, gnn_dim*2]
        
        # Attention-based feature weighting
        attention_weights = torch.sigmoid(self.fc1(compound_graph_feat)).mean(dim=1, keepdim=True)  # [B, 1, compound_text_dim]
        attention_weights = attention_weights.expand(-1, xd_f2.size(1), -1)  # [B, seq_len, compound_text_dim]
        weighted_seq_feat = xd_f2 * attention_weights  # 加权序列特征
        
        # Concatenate enhanced features
        combined_features = torch.cat([graph_pooled, weighted_seq_feat, xd_f2], dim=-1)  # [B, seq_len, gnn_dim*2 + compound_text_dim*2]
        
        # MLP fusion for explicit coupling with residual connection
        fused_features = self.graph_seq_fusion_mlp(combined_features)  # [B, seq_len, decoder_dim]
        residual = self.fusion_residual_proj(xd_f2)  # [B, seq_len, decoder_dim]
        fused_features = self.fusion_ln(fused_features + residual)  # 残差连接 + LayerNorm

        compound_mask = self.generate_masks(fused_features, data["COMPOUND_SMILES_LENGTH"], self.linear_heads)

        # Cross attention with fused features
        xd = self.cross_atten(fused_features, xd_f1, smiles_mask, node_mask)
        del fused_features, xd_f1, graph_pooled, combined_features, weighted_seq_feat, attention_weights, residual, graph_mean, graph_max

        # compound_mask = self.generate_masks(xd, data['COMPOUND_SMILES_LENGTH'], 10)
        xd_attn = self.drug_attn(xd, compound_mask)
        # 不要删除xd，后面还要用到

        #Protein
        # target_mask = self.generate_attention_mask(256, 4, data["PROTEIN_NODE_NUM"])
        seq_emb = self.transformer_encoder2(self.fc2(target_emb))
        del target_emb

        xt = self.target_ln(seq_emb)
        del seq_emb

        protein_mask = self.generate_masks(xt, data["PROTEIN_NODE_NUM"], self.linear_heads)
        xt_attn = self.target_attn(xt,protein_mask)

        # 修复：应该连接xt和xd，而不是xt和xd_attn
        cat_f = torch.cat([xt, xd], dim=1)
        cat_mask = torch.cat([protein_mask, compound_mask], dim=-1)
        cat_attn = self.inter_attn_one(cat_f, cat_mask)
        del cat_f, cat_mask, xt, xd

        #add some dense layers
        out = self.lin(torch.cat([xd_attn,cat_attn,xt_attn],dim=-1))
        del xd_attn, cat_attn, xt_attn

        return out