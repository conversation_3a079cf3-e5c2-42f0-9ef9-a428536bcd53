"""
增强版核心模型 - 包含更深的架构和改进的特征融合
"""
import math
import torch
from models.mlp_modules import MultiLayerMLP
from models.gnn_enhanced import EnhancedMultiGIN
from torch import nn
import torch.nn.functional as F
from models.decoder import DecoderLayer
from torch.nn.utils.weight_norm import weight_norm
import os


os.environ['CUDA_VISIBLE_DEVICES'] = '0,1,2,3'


class LinearAttention(nn.Module):
    """线性注意力模块"""
    def __init__(self, input_dim=128, hidden_dim=32, heads=10):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.heads = heads

        self.linear_first = torch.nn.Linear(self.input_dim, self.hidden_dim)
        self.linear_second = torch.nn.Linear(self.hidden_dim, self.heads)
        self.softmax = nn.Softmax(dim=-1)

    def forward(self, x, masks):
        sentence_att = F.tanh(self.linear_first(x))
        sentence_att = self.linear_second(sentence_att)
        sentence_att = sentence_att.transpose(1, 2)
        minus_inf = -9e15 * torch.ones_like(sentence_att)
        e = torch.where(masks > 0.5, sentence_att, minus_inf)  # (B,heads,seq_len)
        att = self.softmax(e)
        sentence_embed = att @ x
        avg_sentence_embed = torch.sum(sentence_embed, 1) / self.heads

        return avg_sentence_embed


class GatedFusion(nn.Module):
    """门控融合模块 - 自适应融合图特征和序列特征"""
    def __init__(self, graph_dim, seq_dim, out_dim, dropout=0.1):
        super().__init__()
        self.graph_proj = nn.Linear(graph_dim, out_dim)
        self.seq_proj = nn.Linear(seq_dim, out_dim)
        
        # 门控机制
        self.gate = nn.Sequential(
            nn.Linear(graph_dim + seq_dim, out_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(out_dim, out_dim),
            nn.Sigmoid()
        )
        
        self.layer_norm = nn.LayerNorm(out_dim)
    
    def forward(self, graph_feat, seq_feat):
        """
        graph_feat: [batch_size, seq_len, graph_dim]
        seq_feat: [batch_size, seq_len, seq_dim]
        """
        # 特征投影
        graph_proj = self.graph_proj(graph_feat)
        seq_proj = self.seq_proj(seq_feat)
        
        # 计算门控值
        gate_input = torch.cat([graph_feat, seq_feat], dim=-1)
        gate_value = self.gate(gate_input)
        
        # 门控融合
        fused = gate_value * graph_proj + (1 - gate_value) * seq_proj
        fused = self.layer_norm(fused)
        
        return fused


class CoAttention(nn.Module):
    """协同注意力模块 - 增强药物-蛋白质交互建模"""
    def __init__(self, drug_dim, protein_dim, hidden_dim, num_heads=8, dropout=0.1):
        super().__init__()
        
        # 投影到相同维度
        self.drug_proj = nn.Linear(drug_dim, hidden_dim)
        self.protein_proj = nn.Linear(protein_dim, hidden_dim)
        
        # 自注意力
        self.drug_self_attn = nn.MultiheadAttention(hidden_dim, num_heads, dropout=dropout, batch_first=True)
        self.protein_self_attn = nn.MultiheadAttention(hidden_dim, num_heads, dropout=dropout, batch_first=True)
        
        # 交叉注意力
        self.drug_to_protein_attn = nn.MultiheadAttention(hidden_dim, num_heads, dropout=dropout, batch_first=True)
        self.protein_to_drug_attn = nn.MultiheadAttention(hidden_dim, num_heads, dropout=dropout, batch_first=True)
        
        # Layer Normalization
        self.drug_ln1 = nn.LayerNorm(hidden_dim)
        self.drug_ln2 = nn.LayerNorm(hidden_dim)
        self.protein_ln1 = nn.LayerNorm(hidden_dim)
        self.protein_ln2 = nn.LayerNorm(hidden_dim)
        
        # Feed-forward
        self.drug_ff = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 4, hidden_dim),
            nn.Dropout(dropout)
        )
        self.protein_ff = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 4, hidden_dim),
            nn.Dropout(dropout)
        )
        
    def forward(self, drug_feat, protein_feat, drug_mask=None, protein_mask=None):
        """
        drug_feat: [batch_size, drug_len, drug_dim]
        protein_feat: [batch_size, protein_len, protein_dim]
        """
        # 投影
        drug_h = self.drug_proj(drug_feat)
        protein_h = self.protein_proj(protein_feat)
        
        # 自注意力
        drug_self, _ = self.drug_self_attn(drug_h, drug_h, drug_h, key_padding_mask=drug_mask)
        protein_self, _ = self.protein_self_attn(protein_h, protein_h, protein_h, key_padding_mask=protein_mask)
        
        drug_h = self.drug_ln1(drug_h + drug_self)
        protein_h = self.protein_ln1(protein_h + protein_self)
        
        # 交叉注意力
        drug_to_protein, _ = self.drug_to_protein_attn(drug_h, protein_h, protein_h, key_padding_mask=protein_mask)
        protein_to_drug, _ = self.protein_to_drug_attn(protein_h, drug_h, drug_h, key_padding_mask=drug_mask)
        
        drug_h = self.drug_ln2(drug_h + drug_to_protein)
        protein_h = self.protein_ln2(protein_h + protein_to_drug)
        
        # Feed-forward
        drug_h = drug_h + self.drug_ff(drug_h)
        protein_h = protein_h + self.protein_ff(protein_h)
        
        return drug_h, protein_h


class EnhancedPMMRNet(torch.nn.Module):
    """增强版PMMR网络"""
    def __init__(self, args):
        super(EnhancedPMMRNet, self).__init__()

        self.gnn_dim = args.compound_gnn_dim
        self.dropout = args.dropout
        self.decoder_dim = args.decoder_dim
        self.decoder_heads = args.decoder_heads
        self.compound_text_dim = args.compound_text_dim
        self.compound_structure_dim = args.compound_structure_dim
        self.protein_dim = args.protein_dim
        self.linear_heads = args.linear_heads
        self.linears_hidden_dim = args.linear_hidden_dim
        self.feedforward_dim = args.pf_dim
        self.encoder_heads = args.encoder_heads
        self.encoder_layers = args.encoder_layers
        self.protein_pretrained_dim = args.protein_pretrained_dim
        self.compound_pretrained_dim = args.compound_pretrained_dim
        self.objective = args.objective
        self.gnn_layers = args.gnn_layers

        # ===================== 增强的GIN层 =====================
        self.drug_gin = EnhancedMultiGIN(
            self.compound_structure_dim, 
            self.gnn_dim,
            num_layers=self.gnn_layers,
            eps=0.0,
            train_eps=True,
            dropout=self.dropout * 0.5  # GIN层使用较小的dropout
        )
        
        # ===================== 门控融合模块 =====================
        self.gated_fusion = GatedFusion(
            self.gnn_dim, 
            self.compound_text_dim, 
            self.decoder_dim,
            dropout=self.dropout
        )
        
        # ===================== 原始的MLP融合（备用）=====================
        self.graph_seq_fusion_mlp = MultiLayerMLP(
            self.gnn_dim * 2 + self.compound_text_dim * 2, 
            self.decoder_dim, 
            self.dropout
        )
        
        # 残差投影
        self.fusion_residual_proj = nn.Linear(self.compound_text_dim, self.decoder_dim)
        self.fusion_ln = nn.LayerNorm(self.decoder_dim)

        # ===================== 交叉注意力 =====================
        self.cross_atten = DecoderLayer(self.decoder_dim, self.decoder_heads, self.dropout)

        # ===================== 协同注意力（可选）=====================
        self.use_co_attention = True
        if self.use_co_attention:
            self.co_attention = CoAttention(
                self.decoder_dim, 
                self.protein_dim,
                self.decoder_dim,
                num_heads=self.decoder_heads,
                dropout=self.dropout
            )

        # ===================== 线性注意力 =====================
        self.drug_attn = LinearAttention(self.compound_text_dim, self.linears_hidden_dim, self.linear_heads)
        self.target_attn = LinearAttention(self.protein_dim, self.linears_hidden_dim, self.linear_heads)
        self.inter_attn_one = LinearAttention(self.protein_dim, self.linears_hidden_dim, self.linear_heads)

        # ===================== Transformer编码器 =====================
        self.encoder_layer = nn.TransformerEncoderLayer(
            d_model=self.compound_text_dim, 
            dim_feedforward=self.feedforward_dim, 
            nhead=self.encoder_heads,
            dropout=self.dropout,
            batch_first=False
        )
        self.transformer_encoder = nn.TransformerEncoder(self.encoder_layer, num_layers=self.encoder_layers)

        self.encoder_layer2 = nn.TransformerEncoderLayer(
            d_model=self.protein_dim, 
            dim_feedforward=self.feedforward_dim, 
            nhead=self.encoder_heads,
            dropout=self.dropout,
            batch_first=False
        )
        self.transformer_encoder2 = nn.TransformerEncoder(self.encoder_layer2, num_layers=self.encoder_layers)

        # ===================== 特征投影层 =====================
        self.fc1 = nn.Linear(self.gnn_dim, self.compound_text_dim)
        self.fc2 = nn.Linear(self.protein_pretrained_dim, self.protein_dim)
        self.fc3 = nn.Linear(self.compound_pretrained_dim, self.compound_text_dim)

        self.drug_ln = nn.LayerNorm(self.compound_text_dim)
        self.target_ln = nn.LayerNorm(self.protein_dim)

        # ===================== 预测头 =====================
        if self.objective == 'regression':
            # 更深的预测头，使用更大的隐藏层
            self.lin = nn.Sequential(
                nn.Linear(self.protein_dim * 3, 1024),
                nn.BatchNorm1d(1024),
                nn.ReLU(),
                nn.Dropout(self.dropout),

                nn.Linear(1024, 512),
                nn.BatchNorm1d(512),
                nn.ReLU(),
                nn.Dropout(self.dropout),

                nn.Linear(512, 256),
                nn.BatchNorm1d(256),
                nn.ReLU(),
                nn.Dropout(self.dropout * 0.5),

                nn.Linear(256, 128),
                nn.BatchNorm1d(128),
                nn.ReLU(),
                nn.Dropout(self.dropout * 0.5),

                nn.Linear(128, 1)
            )
        elif self.objective == 'classification':
            self.lin = nn.Sequential(
                nn.Linear(self.protein_dim * 3, 512), 
                nn.ReLU(), 
                nn.Dropout(self.dropout),
                nn.Linear(512, 2)
            )

    def generate_masks(self, adj, adj_sizes, n_heads):
        """生成掩码"""
        out = torch.ones(adj.shape[0], adj.shape[1])
        max_size = adj.shape[1]
        if isinstance(adj_sizes, int):
            out[0, adj_sizes:max_size] = 0
        else:
            for e_id, drug_len in enumerate(adj_sizes):
                out[e_id, drug_len: max_size] = 0
        out = out.unsqueeze(1).expand(-1, n_heads, -1)
        return out.to(device=adj.device)

    def make_masks(self, atom_num, protein_num, compound_max_len, protein_max_len):
        """创建掩码"""
        batch_size = len(atom_num)
        device = next(self.parameters()).device
        compound_mask = torch.zeros((batch_size, compound_max_len), device=device)
        protein_mask = torch.zeros((batch_size, protein_max_len), device=device)

        for i in range(batch_size):
            compound_mask[i, :atom_num[i]] = 1
            protein_mask[i, :protein_num[i]] = 1
        compound_mask = compound_mask.unsqueeze(1).unsqueeze(2)
        protein_mask = protein_mask.unsqueeze(1).unsqueeze(2)
        return compound_mask, protein_mask

    def forward(self, data):
        device = next(self.parameters()).device

        # 移动数据到设备
        compound_x = data['COMPOUND_NODE_FEAT'].to(device, non_blocking=True)
        compound_adj = data['COMPOUND_ADJ'].to(device, non_blocking=True)
        compound_emb = data['COMPOUND_EMBEDDING'].to(device, non_blocking=True)
        target_emb = data['PROTEIN_EMBEDDING'].to(device, non_blocking=True)

        compound_smiles_max_len = data['COMPOUND_EMBEDDING'].shape[1]
        compound_node_max_len = data['COMPOUND_NODE_FEAT'].shape[1]

        node_mask, smiles_mask = self.make_masks(
            data["COMPOUND_NODE_NUM"],
            data["COMPOUND_SMILES_LENGTH"],
            compound_node_max_len,
            compound_smiles_max_len,
        )

        # ===================== Drug分支 =====================
        # Step 1: 增强的GIN提取分子图特征
        compound_graph_feat = self.drug_gin(compound_x, compound_adj)
        del compound_x, compound_adj

        xd_f1 = self.drug_ln(self.fc1(compound_graph_feat))

        # Step 2: Transformer提取SMILES序列特征
        compound_smiles = self.transformer_encoder(self.fc3(compound_emb))
        del compound_emb

        xd_f2 = self.drug_ln(compound_smiles)
        del compound_smiles

        # Step 3: 特征融合
        # 多尺度图池化
        graph_mean = torch.mean(compound_graph_feat, dim=1, keepdim=True)
        graph_max = torch.max(compound_graph_feat, dim=1, keepdim=True)[0]
        graph_pooled = torch.cat([graph_mean, graph_max], dim=-1)
        graph_pooled = graph_pooled.expand(-1, xd_f2.size(1), -1)
        
        # 注意力加权
        attention_weights = torch.sigmoid(self.fc1(compound_graph_feat)).mean(dim=1, keepdim=True)
        attention_weights = attention_weights.expand(-1, xd_f2.size(1), -1)
        weighted_seq_feat = xd_f2 * attention_weights
        
        # 组合特征
        combined_features = torch.cat([graph_pooled, weighted_seq_feat, xd_f2], dim=-1)
        
        # MLP融合
        fused_features = self.graph_seq_fusion_mlp(combined_features)
        residual = self.fusion_residual_proj(xd_f2)
        fused_features = self.fusion_ln(fused_features + residual)

        compound_mask = self.generate_masks(fused_features, data["COMPOUND_SMILES_LENGTH"], self.linear_heads)

        # 交叉注意力
        xd = self.cross_atten(fused_features, xd_f1, smiles_mask, node_mask)
        del fused_features, xd_f1, graph_pooled, combined_features, weighted_seq_feat, attention_weights, residual, graph_mean, graph_max

        xd_attn = self.drug_attn(xd, compound_mask)

        # ===================== Protein分支 =====================
        seq_emb = self.transformer_encoder2(self.fc2(target_emb))
        del target_emb

        xt = self.target_ln(seq_emb)
        del seq_emb

        protein_mask = self.generate_masks(xt, data["PROTEIN_NODE_NUM"], self.linear_heads)
        xt_attn = self.target_attn(xt, protein_mask)

        # ===================== 药物-蛋白质交互 =====================
        if self.use_co_attention:
            # 使用协同注意力
            xd_enhanced, xt_enhanced = self.co_attention(xd, xt)
            cat_f = torch.cat([xt_enhanced, xd_enhanced], dim=1)
        else:
            # 原始方式
            cat_f = torch.cat([xt, xd], dim=1)
        
        cat_mask = torch.cat([protein_mask, compound_mask], dim=-1)
        cat_attn = self.inter_attn_one(cat_f, cat_mask)
        del cat_f, cat_mask, xt, xd

        # ===================== 预测 =====================
        out = self.lin(torch.cat([xd_attn, cat_attn, xt_attn], dim=-1))
        del xd_attn, cat_attn, xt_attn

        return out


# 为了兼容，保留原来的类名
class PMMRNet(EnhancedPMMRNet):
    """向后兼容的包装类"""
    pass

