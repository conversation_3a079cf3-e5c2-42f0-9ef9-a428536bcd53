"""
增强版GNN模块 - 包含更深的GIN层和残差连接
"""
import math
import torch
import torch.nn as nn
import torch.nn.functional as F


class GINLayer(nn.Module):
    """Graph Isomorphism Network Layer"""

    def __init__(self, input_features, output_features, eps=0.0, train_eps=True):
        super(GINLayer, self).__init__()
        self.input_features = input_features
        self.output_features = output_features

        # MLP for node feature transformation - 加深MLP
        self.mlp = nn.Sequential(
            nn.Linear(input_features, output_features * 2),
            nn.BatchNorm1d(output_features * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(output_features * 2, output_features),
            nn.BatchNorm1d(output_features),
            nn.ReLU()
        )

        # Epsilon parameter for self-loop weighting
        if train_eps:
            self.eps = nn.Parameter(torch.tensor([eps], dtype=torch.float32))
        else:
            self.register_buffer('eps', torch.tensor([eps], dtype=torch.float32))

    def forward(self, adj, x):
        # GIN aggregation: h_v^(l+1) = MLP((1 + eps) * h_v^(l) + sum(h_u^(l) for u in N(v)))
        if len(x.shape) == 3:  # Batch mode
            # Aggregate neighbor features
            neighbor_sum = torch.bmm(adj, x)  # [batch_size, num_nodes, input_features]
            # Add self features with epsilon weighting
            out = (1 + self.eps) * x + neighbor_sum
            
            # 处理batch normalization
            batch_size, num_nodes, feat_dim = out.shape
            out = out.view(-1, feat_dim)  # [batch_size * num_nodes, feat_dim]
            out = self.mlp(out)
            out = out.view(batch_size, num_nodes, -1)  # [batch_size, num_nodes, output_features]
        else:  # Single graph mode
            # Aggregate neighbor features
            neighbor_sum = torch.mm(adj, x)  # [num_nodes, input_features]
            # Add self features with epsilon weighting
            out = (1 + self.eps) * x + neighbor_sum
            # Apply MLP transformation
            out = self.mlp(out)
        
        return out


class EnhancedMultiGIN(nn.Module):
    """增强版多层GIN - 包含更深层数、残差连接和Batch Normalization"""

    def __init__(self, in_dim, out_dim, num_layers=5, eps=0.0, train_eps=True, dropout=0.1):
        super(EnhancedMultiGIN, self).__init__()
        
        self.num_layers = num_layers
        self.layers = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        self.dropout = dropout
        
        # 第一层：输入维度 -> 输出维度
        self.layers.append(GINLayer(in_dim, out_dim, eps=eps, train_eps=train_eps))
        self.batch_norms.append(nn.BatchNorm1d(out_dim))
        
        # 中间层：输出维度 -> 输出维度
        for _ in range(num_layers - 1):
            self.layers.append(GINLayer(out_dim, out_dim, eps=eps, train_eps=train_eps))
            self.batch_norms.append(nn.BatchNorm1d(out_dim))
        
        # 残差连接的投影层
        if in_dim != out_dim:
            self.residual_proj = nn.Linear(in_dim, out_dim)
        else:
            self.residual_proj = None

    def forward(self, inputs, adj):
        """
        inputs: [batch_size, num_nodes, in_dim]
        adj: [batch_size, num_nodes, num_nodes]
        """
        h = inputs
        
        for i, (layer, bn) in enumerate(zip(self.layers, self.batch_norms)):
            # GIN层
            h_new = layer(adj, h)
            
            # 残差连接（从第二层开始）
            if i > 0:
                h_new = h_new + h
            elif self.residual_proj is not None:
                # 第一层如果维度不匹配，使用投影
                h_proj = self.residual_proj(inputs)
                h_new = h_new + h_proj
            
            # Dropout
            if self.dropout > 0:
                h_new = F.dropout(h_new, p=self.dropout, training=self.training)
            
            h = h_new
        
        return h


class MultiScaleGIN(nn.Module):
    """多尺度GIN - 结合不同层的特征"""

    def __init__(self, in_dim, out_dim, num_layers=5, eps=0.0, train_eps=True, dropout=0.1):
        super(MultiScaleGIN, self).__init__()
        
        self.num_layers = num_layers
        self.layers = nn.ModuleList()
        self.dropout = dropout
        
        # 第一层
        self.layers.append(GINLayer(in_dim, out_dim, eps=eps, train_eps=train_eps))
        
        # 中间层
        for _ in range(num_layers - 1):
            self.layers.append(GINLayer(out_dim, out_dim, eps=eps, train_eps=train_eps))
        
        # 多尺度融合层
        self.fusion = nn.Sequential(
            nn.Linear(out_dim * num_layers, out_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(out_dim * 2, out_dim)
        )
        
        # 残差连接投影
        if in_dim != out_dim:
            self.residual_proj = nn.Linear(in_dim, out_dim)
        else:
            self.residual_proj = None

    def forward(self, inputs, adj):
        """
        inputs: [batch_size, num_nodes, in_dim]
        adj: [batch_size, num_nodes, num_nodes]
        """
        h = inputs
        layer_outputs = []
        
        for i, layer in enumerate(self.layers):
            h = F.relu(layer(adj, h))
            if self.dropout > 0:
                h = F.dropout(h, p=self.dropout, training=self.training)
            layer_outputs.append(h)
        
        # 多尺度特征融合
        multi_scale_feat = torch.cat(layer_outputs, dim=-1)  # [batch_size, num_nodes, out_dim * num_layers]
        
        # 通过融合层
        batch_size, num_nodes, feat_dim = multi_scale_feat.shape
        multi_scale_feat = multi_scale_feat.view(-1, feat_dim)
        fused = self.fusion(multi_scale_feat)
        fused = fused.view(batch_size, num_nodes, -1)
        
        # 残差连接
        if self.residual_proj is not None:
            residual = self.residual_proj(inputs)
            fused = fused + residual
        
        return fused


class AttentiveGIN(nn.Module):
    """注意力增强的GIN - 使用注意力机制聚合邻居"""

    def __init__(self, in_dim, out_dim, num_layers=5, num_heads=4, eps=0.0, train_eps=True, dropout=0.1):
        super(AttentiveGIN, self).__init__()
        
        self.num_layers = num_layers
        self.num_heads = num_heads
        self.dropout = dropout
        
        # GIN层
        self.gin_layers = nn.ModuleList()
        self.gin_layers.append(GINLayer(in_dim, out_dim, eps=eps, train_eps=train_eps))
        for _ in range(num_layers - 1):
            self.gin_layers.append(GINLayer(out_dim, out_dim, eps=eps, train_eps=train_eps))
        
        # 注意力层
        self.attention_layers = nn.ModuleList()
        for _ in range(num_layers):
            self.attention_layers.append(
                nn.MultiheadAttention(out_dim, num_heads, dropout=dropout, batch_first=True)
            )
        
        # Layer Normalization
        self.layer_norms = nn.ModuleList()
        for _ in range(num_layers):
            self.layer_norms.append(nn.LayerNorm(out_dim))
        
        # 残差投影
        if in_dim != out_dim:
            self.residual_proj = nn.Linear(in_dim, out_dim)
        else:
            self.residual_proj = None

    def forward(self, inputs, adj):
        """
        inputs: [batch_size, num_nodes, in_dim]
        adj: [batch_size, num_nodes, num_nodes]
        """
        h = inputs
        
        for i, (gin_layer, attn_layer, ln) in enumerate(zip(self.gin_layers, self.attention_layers, self.layer_norms)):
            # GIN聚合
            h_gin = F.relu(gin_layer(adj, h))
            
            # 自注意力
            h_attn, _ = attn_layer(h_gin, h_gin, h_gin)
            
            # 残差连接
            if i == 0 and self.residual_proj is not None:
                h_res = self.residual_proj(h)
            else:
                h_res = h
            
            # 组合
            h = ln(h_gin + h_attn + h_res)
            
            # Dropout
            if self.dropout > 0:
                h = F.dropout(h, p=self.dropout, training=self.training)
        
        return h


# 为了兼容性，保留原来的MultiGIN接口
class MultiGIN(nn.Module):
    """兼容版MultiGIN - 可以选择不同的实现"""

    def __init__(self, in_dim, out_dim, version='enhanced', **kwargs):
        super(MultiGIN, self).__init__()
        
        if version == 'enhanced':
            self.gin = EnhancedMultiGIN(in_dim, out_dim, **kwargs)
        elif version == 'multiscale':
            self.gin = MultiScaleGIN(in_dim, out_dim, **kwargs)
        elif version == 'attentive':
            self.gin = AttentiveGIN(in_dim, out_dim, **kwargs)
        else:  # basic
            self.gin = BasicMultiGIN(in_dim, out_dim)

    def forward(self, inputs, adj):
        return self.gin(inputs, adj)


class BasicMultiGIN(nn.Module):
    """基础版MultiGIN - 与原始代码兼容"""

    def __init__(self, in_dim, out_dim, eps=0.0, train_eps=False):
        super(BasicMultiGIN, self).__init__()

        # Three GIN layers with MLP transformations
        self.gin1 = GINLayer(in_dim, out_dim, eps=eps, train_eps=train_eps)
        self.gin2 = GINLayer(out_dim, out_dim, eps=eps, train_eps=train_eps)
        self.gin3 = GINLayer(out_dim, out_dim, eps=eps, train_eps=train_eps)

    def forward(self, inputs, adj):
        # inputs: [batch_size, num_nodes, in_dim]
        # adj: [batch_size, num_nodes, num_nodes]
        outputs = F.relu(self.gin1(adj, inputs))
        outputs = F.relu(self.gin2(adj, outputs))
        outputs = F.relu(self.gin3(adj, outputs))
        return outputs

