import torch
import torch.nn as nn
import torch.nn.functional as F


class ThreeLayerMLP(nn.Module):
    """
    三层MLP模块，用于替换GCN/GIN模块
    """
    def __init__(self, input_dim, hidden_dim, output_dim, dropout=0.1, activation='relu'):
        super(ThreeLayerMLP, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.dropout = dropout
        
        # 三层线性层
        self.layer1 = nn.Linear(input_dim, hidden_dim)
        self.layer2 = nn.Linear(hidden_dim, hidden_dim)
        self.layer3 = nn.Linear(hidden_dim, output_dim)
        
        # Dropout层
        self.dropout_layer = nn.Dropout(dropout)
        
        # 激活函数
        if activation == 'relu':
            self.activation = F.relu
        elif activation == 'gelu':
            self.activation = F.gelu
        elif activation == 'tanh':
            self.activation = torch.tanh
        else:
            self.activation = F.relu
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)
    
    def forward(self, x, adj=None):
        """
        前向传播
        Args:
            x: 输入特征 [batch_size, seq_len, input_dim] 或 [batch_size, num_nodes, input_dim]
            adj: 邻接矩阵（为了兼容性保留，但不使用）
        Returns:
            输出特征 [batch_size, seq_len, output_dim] 或 [batch_size, num_nodes, output_dim]
        """
        # 第一层
        h1 = self.layer1(x)
        h1 = self.activation(h1)
        h1 = self.dropout_layer(h1)
        
        # 第二层
        h2 = self.layer2(h1)
        h2 = self.activation(h2)
        h2 = self.dropout_layer(h2)
        
        # 第三层
        h3 = self.layer3(h2)
        
        return h3


class MLPPositionwiseFeedforward(nn.Module):
    """
    MLP前馈网络，替换GCN前馈网络
    """
    def __init__(self, hid_dim, pf_dim, dropout):
        super(MLPPositionwiseFeedforward, self).__init__()
        
        self.hid_dim = hid_dim
        self.pf_dim = pf_dim
        
        # 使用三层MLP
        self.mlp = ThreeLayerMLP(hid_dim, pf_dim, hid_dim, dropout)
    
    def forward(self, x):
        """
        前向传播
        Args:
            x: 输入特征 [batch_size, seq_len, hid_dim]
        Returns:
            输出特征 [batch_size, seq_len, hid_dim]
        """
        return self.mlp(x)


class MultiLayerMLP(nn.Module):
    """
    多层MLP，替换MultiGIN/MultiGCN
    """
    def __init__(self, in_dim, out_dim, dropout=0.1):
        super(MultiLayerMLP, self).__init__()
        
        self.in_dim = in_dim
        self.out_dim = out_dim
        
        # 使用三层MLP结构
        self.mlp = ThreeLayerMLP(in_dim, out_dim, out_dim, dropout)
    
    def forward(self, inputs, adj=None):
        """
        前向传播
        Args:
            inputs: 输入特征 [batch_size, num_nodes, in_dim]
            adj: 邻接矩阵（为了兼容性保留，但不使用）
        Returns:
            输出特征 [batch_size, num_nodes, out_dim]
        """
        return self.mlp(inputs)


class ResidualMLP(nn.Module):
    """
    带残差连接的MLP，用于更深的网络
    """
    def __init__(self, input_dim, hidden_dim, output_dim, dropout=0.1):
        super(ResidualMLP, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        
        # 主要的MLP路径
        self.mlp = ThreeLayerMLP(input_dim, hidden_dim, output_dim, dropout)
        
        # 残差连接的投影层（如果输入输出维度不同）
        self.residual_proj = None
        if input_dim != output_dim:
            self.residual_proj = nn.Linear(input_dim, output_dim)
    
    def forward(self, x, adj=None):
        """
        前向传播，带残差连接
        """
        # 主要路径
        out = self.mlp(x)
        
        # 残差连接
        if self.residual_proj is not None:
            residual = self.residual_proj(x)
        else:
            residual = x
        
        return out + residual


class AdaptiveMLP(nn.Module):
    """
    自适应MLP，可以根据输入动态调整
    """
    def __init__(self, input_dim, hidden_dim, output_dim, dropout=0.1):
        super(AdaptiveMLP, self).__init__()
        
        # 基础MLP
        self.base_mlp = ThreeLayerMLP(input_dim, hidden_dim, output_dim, dropout)
        
        # 注意力权重生成
        self.attention_layer = nn.Sequential(
            nn.Linear(input_dim, hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x, adj=None):
        """
        前向传播，使用注意力机制
        """
        # 生成注意力权重
        attention_weights = self.attention_layer(x)  # [batch_size, seq_len, 1]
        
        # 应用基础MLP
        mlp_out = self.base_mlp(x)
        
        # 应用注意力权重
        weighted_out = mlp_out * attention_weights
        
        return weighted_out
