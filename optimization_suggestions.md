# 药物-蛋白质相互作用预测模型优化方案

## 当前模型性能 (<PERSON>数据集)
- RMSE: 0.443
- MSE: 0.196
- MAE: 0.234
- <PERSON>: 0.878
- <PERSON><PERSON><PERSON>: 0.713
- CI: 0.913

---

## 🔥 优化方案（按优先级排序）

### 1. 模型架构优化 ⭐⭐⭐⭐⭐

#### 1.1 增加GIN网络深度
**当前问题**: GIN只有3层，表征能力有限
**优化方案**:
- 将GIN层数增加到5-7层
- 添加残差连接防止梯度消失
- 使用不同的聚合策略（sum, mean, max pooling组合）

```python
# 修改 models/gnn.py 中的 MultiGIN
class EnhancedMultiGIN(nn.Module):
    def __init__(self, in_dim, out_dim, num_layers=5, eps=0.0, train_eps=True):
        super().__init__()
        self.layers = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        
        # 第一层
        self.layers.append(GINLayer(in_dim, out_dim, eps, train_eps))
        self.batch_norms.append(nn.BatchNorm1d(out_dim))
        
        # 中间层
        for _ in range(num_layers - 1):
            self.layers.append(GINLayer(out_dim, out_dim, eps, train_eps))
            self.batch_norms.append(nn.BatchNorm1d(out_dim))
    
    def forward(self, inputs, adj):
        h = inputs
        for i, (layer, bn) in enumerate(zip(self.layers, self.batch_norms)):
            h_new = F.relu(layer(adj, h))
            # 应用batch normalization
            if len(h_new.shape) == 3:
                h_new = bn(h_new.transpose(1, 2)).transpose(1, 2)
            # 残差连接（从第二层开始）
            if i > 0:
                h_new = h_new + h
            h = h_new
        return h
```

**预期提升**: Pearson +0.01-0.02, CI +0.005-0.01

---

#### 1.2 改进特征融合策略
**当前问题**: 图特征和序列特征融合较简单
**优化方案**:
- 使用门控机制自适应融合
- 添加多尺度特征提取
- 引入对比学习损失

```python
# 在 models/core.py 中添加
class GatedFusion(nn.Module):
    def __init__(self, graph_dim, seq_dim, out_dim):
        super().__init__()
        self.graph_proj = nn.Linear(graph_dim, out_dim)
        self.seq_proj = nn.Linear(seq_dim, out_dim)
        self.gate = nn.Sequential(
            nn.Linear(graph_dim + seq_dim, out_dim),
            nn.Sigmoid()
        )
    
    def forward(self, graph_feat, seq_feat):
        gate_input = torch.cat([graph_feat, seq_feat], dim=-1)
        gate_value = self.gate(gate_input)
        
        graph_proj = self.graph_proj(graph_feat)
        seq_proj = self.seq_proj(seq_feat)
        
        fused = gate_value * graph_proj + (1 - gate_value) * seq_proj
        return fused
```

**预期提升**: MAE -0.01-0.02, Spearman +0.02-0.03

---

#### 1.3 增强跨模态注意力
**当前问题**: 药物-蛋白质交互建模不够深入
**优化方案**:
- 添加多层交叉注意力
- 使用协同注意力机制
- 引入位置编码

```python
class CoAttention(nn.Module):
    def __init__(self, drug_dim, protein_dim, hidden_dim, num_heads=8):
        super().__init__()
        self.drug_attn = nn.MultiheadAttention(hidden_dim, num_heads)
        self.protein_attn = nn.MultiheadAttention(hidden_dim, num_heads)
        self.cross_attn = nn.MultiheadAttention(hidden_dim, num_heads)
        
    def forward(self, drug_feat, protein_feat):
        # 自注意力
        drug_self = self.drug_attn(drug_feat, drug_feat, drug_feat)[0]
        protein_self = self.protein_attn(protein_feat, protein_feat, protein_feat)[0]
        
        # 交叉注意力
        drug_to_protein = self.cross_attn(drug_self, protein_self, protein_self)[0]
        protein_to_drug = self.cross_attn(protein_self, drug_self, drug_self)[0]
        
        return drug_to_protein, protein_to_drug
```

**预期提升**: Pearson +0.01-0.015, CI +0.008-0.012

---

### 2. 训练策略优化 ⭐⭐⭐⭐⭐

#### 2.1 使用更好的优化器和学习率调度
**当前问题**: 使用基础Adam + ReduceLROnPlateau
**优化方案**:

```python
# 替换优化器为AdamW with weight decay
optimizer = torch.optim.AdamW(
    model.parameters(), 
    lr=LR,
    weight_decay=0.01,  # L2正则化
    betas=(0.9, 0.999)
)

# 使用CosineAnnealingWarmRestarts替代ReduceLROnPlateau
scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
    optimizer, 
    T_0=50,  # 重启周期
    T_mult=2,  # 周期倍增因子
    eta_min=1e-6
)

# 添加学习率warmup
def warmup_lr_scheduler(optimizer, warmup_epochs=10, base_lr=0.0001):
    def lr_lambda(epoch):
        if epoch < warmup_epochs:
            return (epoch + 1) / warmup_epochs
        return 1.0
    return torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)
```

**预期提升**: 训练稳定性提升, RMSE -0.01-0.02

---

#### 2.2 改进损失函数
**当前问题**: 仅使用MSE损失
**优化方案**: 使用组合损失函数

```python
class CombinedLoss(nn.Module):
    def __init__(self, alpha=0.7, beta=0.2, gamma=0.1):
        super().__init__()
        self.alpha = alpha  # MSE权重
        self.beta = beta    # MAE权重
        self.gamma = gamma  # Smooth L1权重
        
    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        mae_loss = F.l1_loss(pred, target)
        smooth_l1 = F.smooth_l1_loss(pred, target)
        
        return self.alpha * mse_loss + self.beta * mae_loss + self.gamma * smooth_l1

# 使用方法
loss_fn = CombinedLoss()
```

**预期提升**: MAE -0.015-0.025, RMSE -0.01-0.015

---

#### 2.3 添加梯度裁剪和混合精度训练
**优化方案**:

```python
# 梯度裁剪
torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

# 混合精度训练
from torch.cuda.amp import autocast, GradScaler
scaler = GradScaler()

def train_with_amp(model, data, optimizer, loss_fn):
    optimizer.zero_grad()
    with autocast():
        output = model(data)
        loss = loss_fn(output, data['LABEL'].view(-1, 1).float().cuda())
    
    scaler.scale(loss).backward()
    scaler.unscale_(optimizer)
    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
    scaler.step(optimizer)
    scaler.update()
```

**预期提升**: 训练速度提升30-50%，显存占用减少30%

---

### 3. 正则化优化 ⭐⭐⭐⭐

#### 3.1 自适应Dropout
**当前问题**: 固定dropout=0.2可能不够
**优化方案**:

```python
# 不同层使用不同的dropout率
class AdaptiveDropout(nn.Module):
    def __init__(self, base_dropout=0.2):
        super().__init__()
        self.gnn_dropout = 0.1  # GNN层较低dropout
        self.attention_dropout = 0.2  # 注意力层中等dropout
        self.fc_dropout = 0.3  # 全连接层较高dropout
```

**预期提升**: 防止过拟合，Spearman +0.01-0.02

---

#### 3.2 Label Smoothing
**优化方案**:

```python
class LabelSmoothingLoss(nn.Module):
    def __init__(self, smoothing=0.1):
        super().__init__()
        self.smoothing = smoothing
        
    def forward(self, pred, target):
        # 为回归任务添加噪声
        noise = torch.randn_like(target) * self.smoothing
        smoothed_target = target + noise
        return F.mse_loss(pred, smoothed_target)
```

---

### 4. 数据增强 ⭐⭐⭐

#### 4.1 分子图增强
**优化方案**:

```python
class MolecularAugmentation:
    @staticmethod
    def node_dropout(x, adj, drop_rate=0.1):
        # 随机丢弃节点
        mask = torch.rand(x.size(1)) > drop_rate
        x_aug = x[:, mask, :]
        adj_aug = adj[:, mask, :][:, :, mask]
        return x_aug, adj_aug
    
    @staticmethod
    def edge_perturbation(adj, perturb_rate=0.1):
        # 随机扰动边
        mask = torch.rand_like(adj) > perturb_rate
        adj_aug = adj * mask
        return adj_aug
```

**预期提升**: 泛化能力提升，测试集性能 +1-2%

---

### 5. 模型集成 ⭐⭐⭐⭐

#### 5.1 多模型集成
**优化方案**:

```python
# 训练多个具有不同初始化的模型
models = []
for seed in [0, 42, 123, 456, 789]:
    torch.manual_seed(seed)
    model = PMMRNet(args).cuda()
    # 训练模型...
    models.append(model)

# 预测时取平均
def ensemble_predict(models, data):
    predictions = []
    for model in models:
        with torch.no_grad():
            pred = model(data)
            predictions.append(pred)
    return torch.stack(predictions).mean(dim=0)
```

**预期提升**: 所有指标 +2-5%

---

### 6. 超参数优化建议 ⭐⭐⭐⭐

| 参数 | 当前值 | 建议值 | 说明 |
|------|--------|--------|------|
| learning_rate | 0.0001 | 0.0003 → 1e-6 (cosine) | 使用warmup和cosine衰减 |
| batch_size | 256 | 64-128 | 更小的batch获得更好的梯度 |
| dropout | 0.2 | 0.1-0.3 (自适应) | 不同层使用不同dropout |
| gnn_layers | 3 | 5-7 | 更深的GNN |
| decoder_layers | 3 | 4-6 | 更深的decoder |
| decoder_dim | 128 | 256 | 更大的特征维度 |
| pf_dim | 1024 | 2048 | 更大的前馈网络 |
| linear_heads | 10 | 12-16 | 更多注意力头 |
| weight_decay | 0 | 0.01 | 添加L2正则化 |

---

## 🚀 实施步骤建议

### Phase 1: 快速提升 (预期提升: 5-8%)
1. 更换优化器为AdamW + weight_decay
2. 改进学习率调度（CosineAnnealing + Warmup）
3. 使用组合损失函数
4. 添加梯度裁剪
5. 增加模型维度（decoder_dim=256, pf_dim=2048）

### Phase 2: 架构优化 (预期提升: 8-12%)
1. 增加GIN层数到5-7层
2. 改进特征融合（门控机制）
3. 增强交叉注意力
4. 添加残差连接和BatchNorm

### Phase 3: 高级优化 (预期提升: 3-5%)
1. 实现数据增强
2. 使用混合精度训练
3. 模型集成
4. 超参数搜索（Optuna/Ray Tune）

---

## 📈 预期最终性能

保守估计（实施Phase 1+2）:
- **RMSE**: 0.443 → **0.410-0.420** (↓ 5-7%)
- **MAE**: 0.234 → **0.210-0.220** (↓ 6-10%)
- **Pearson**: 0.878 → **0.890-0.900** (↑ 1.4-2.5%)
- **Spearman**: 0.713 → **0.740-0.760** (↑ 3.8-6.6%)
- **CI**: 0.913 → **0.920-0.930** (↑ 0.8-1.9%)

乐观估计（实施全部Phase）:
- **RMSE**: → **0.390-0.410**
- **MAE**: → **0.195-0.210**
- **Pearson**: → **0.900-0.910**
- **Spearman**: → **0.760-0.780**
- **CI**: → **0.930-0.940**

---

## ⚠️ 注意事项

1. **过拟合风险**: 增加模型容量时要注意验证集性能
2. **训练时间**: 更深的模型需要更长训练时间
3. **显存占用**: 增大batch_size前先测试显存
4. **超参数搜索**: 建议使用网格搜索或贝叶斯优化
5. **数据质量**: 确保数据预处理正确

---

## 🛠️ 快速开始

我已经为你准备了一个增强版的训练脚本，包含以上大部分优化。
可以直接运行 `python train_enhanced_v2.py` 开始训练。

