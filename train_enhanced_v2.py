#!/usr/bin/env python3
"""
增强版训练脚本v2 - 集成多项优化策略
包含：
1. AdamW优化器 + weight decay
2. CosineAnnealing学习率调度 + warmup
3. 组合损失函数
4. 梯度裁剪
5. 混合精度训练
6. 更优的超参数配置
"""
import subprocess
import os
import sys

def main():
    """运行增强版训练"""
    
    # 优化后的超参数配置
    enhanced_params = {
        # 基础设置
        'objective': 'regression',
        'dataset': 'davis',
        'batch_size': 96,  # 较小的batch size获得更好的梯度
        'max_epochs': 400,
        'learning_rate': 0.0003,  # 稍高的初始学习率，配合warmup
        
        # 模型架构 - 更大的容量
        'dropout': 0.25,  # 适中的dropout
        'gnn_layers': 5,  # 更深的GIN层
        'decoder_layers': 5,  # 更深的decoder
        'decoder_heads': 8,  # 更多注意力头
        'encoder_heads': 8,
        'linear_heads': 14,  # 更多线性注意力头
        'linear_hidden_dim': 64,
        
        # 增强的特征维度
        'decoder_dim': 256,  # 更大的decoder维度
        'pf_dim': 2048,  # 更大的前馈网络
        'compound_gnn_dim': 128,  # 更大的GNN维度
        'protein_dim': 256,  # 更大的蛋白质维度
        'compound_text_dim': 256,  # 更大的化合物文本维度
        
        # 输出目录
        'output_dir': './outputs_enhanced',
    }
    
    # 构建命令
    cmd = ['python', 'main_enhanced_v2.py']
    for param, value in enhanced_params.items():
        cmd.extend([f'--{param}', str(value)])
    
    print("=" * 70)
    print("🚀 增强版训练脚本 v2.0")
    print("=" * 70)
    print("\n📋 优化策略：")
    print("  ✓ AdamW优化器 + Weight Decay (0.01)")
    print("  ✓ CosineAnnealing学习率 + Warmup (10 epochs)")
    print("  ✓ 组合损失函数 (MSE + MAE + Smooth L1)")
    print("  ✓ 梯度裁剪 (max_norm=1.0)")
    print("  ✓ 混合精度训练 (FP16)")
    print("  ✓ 更深的模型架构 (GIN:5层, Decoder:5层)")
    print("  ✓ 更大的特征维度 (Decoder:256, PF:2048)")
    print("\n⚙️  超参数配置：")
    print("-" * 70)
    for param, value in enhanced_params.items():
        print(f"  {param:25s}: {value}")
    print("-" * 70)
    
    # 创建输出目录
    os.makedirs(enhanced_params['output_dir'], exist_ok=True)
    
    # 运行训练
    print("\n🏃 开始训练...\n")
    try:
        result = subprocess.run(cmd, check=True, cwd=os.getcwd())
        print("\n" + "=" * 70)
        print("✅ 训练成功完成！")
        print("=" * 70)
        return result.returncode
    except subprocess.CalledProcessError as e:
        print("\n" + "=" * 70)
        print(f"❌ 训练失败，返回代码: {e.returncode}")
        print("=" * 70)
        return e.returncode
    except KeyboardInterrupt:
        print("\n" + "=" * 70)
        print("⚠️  训练被用户中断")
        print("=" * 70)
        return 1

if __name__ == '__main__':
    sys.exit(main())

