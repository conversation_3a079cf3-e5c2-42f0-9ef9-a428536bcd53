#!/usr/bin/env python3
"""
训练过程可视化工具
"""
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
import sys


def plot_training_curves(history_file, output_file=None, show=True):
    """绘制训练曲线"""
    
    if not os.path.exists(history_file):
        print(f"❌ 文件不存在: {history_file}")
        return
    
    # 读取历史数据
    df = pd.read_csv(history_file)
    
    # 创建图形
    fig, axes = plt.subplots(2, 3, figsize=(18, 10))
    fig.suptitle(f'训练曲线 - {os.path.basename(history_file)}', fontsize=16, fontweight='bold')
    
    # 颜色
    color_train = '#3498db'  # 蓝色
    color_best = '#e74c3c'   # 红色
    
    # 1. CI曲线
    ax = axes[0, 0]
    ax.plot(df['epoch'], df['ci'], color=color_train, alpha=0.7, linewidth=1.5)
    if 'is_best' in df.columns:
        best_epochs = df[df['is_best'] == True]
        ax.scatter(best_epochs['epoch'], best_epochs['ci'], 
                  color=color_best, s=50, zorder=5, label='Best Epochs')
    ax.set_xlabel('Epoch')
    ax.set_ylabel('CI (Concordance Index)')
    ax.set_title('CI - 越高越好', fontweight='bold')
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    # 2. MAE曲线
    ax = axes[0, 1]
    if 'mae' in df.columns:
        ax.plot(df['epoch'], df['mae'], color=color_train, alpha=0.7, linewidth=1.5)
        if 'is_best' in df.columns:
            best_epochs = df[df['is_best'] == True]
            if 'mae' in best_epochs.columns:
                ax.scatter(best_epochs['epoch'], best_epochs['mae'], 
                          color=color_best, s=50, zorder=5, label='Best Epochs')
        ax.set_xlabel('Epoch')
        ax.set_ylabel('MAE (Mean Absolute Error)')
        ax.set_title('MAE - 越低越好', fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.legend()
    
    # 3. RMSE曲线
    ax = axes[0, 2]
    ax.plot(df['epoch'], df['rmse'], color=color_train, alpha=0.7, linewidth=1.5)
    if 'is_best' in df.columns:
        best_epochs = df[df['is_best'] == True]
        ax.scatter(best_epochs['epoch'], best_epochs['rmse'], 
                  color=color_best, s=50, zorder=5, label='Best Epochs')
    ax.set_xlabel('Epoch')
    ax.set_ylabel('RMSE')
    ax.set_title('RMSE - 越低越好', fontweight='bold')
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    # 4. Pearson相关系数
    ax = axes[1, 0]
    ax.plot(df['epoch'], df['pearson'], color=color_train, alpha=0.7, linewidth=1.5)
    if 'is_best' in df.columns:
        best_epochs = df[df['is_best'] == True]
        ax.scatter(best_epochs['epoch'], best_epochs['pearson'], 
                  color=color_best, s=50, zorder=5, label='Best Epochs')
    ax.set_xlabel('Epoch')
    ax.set_ylabel('Pearson Correlation')
    ax.set_title('Pearson相关系数 - 越高越好', fontweight='bold')
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    # 5. Spearman相关系数
    ax = axes[1, 1]
    ax.plot(df['epoch'], df['spearman'], color=color_train, alpha=0.7, linewidth=1.5)
    if 'is_best' in df.columns:
        best_epochs = df[df['is_best'] == True]
        ax.scatter(best_epochs['epoch'], best_epochs['spearman'], 
                  color=color_best, s=50, zorder=5, label='Best Epochs')
    ax.set_xlabel('Epoch')
    ax.set_ylabel('Spearman Correlation')
    ax.set_title('Spearman相关系数 - 越高越好', fontweight='bold')
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    # 6. 学习率曲线（如果有）
    ax = axes[1, 2]
    if 'learning_rate' in df.columns:
        ax.plot(df['epoch'], df['learning_rate'], color='#2ecc71', alpha=0.7, linewidth=1.5)
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Learning Rate')
        ax.set_title('学习率变化', fontweight='bold')
        ax.set_yscale('log')
        ax.grid(True, alpha=0.3)
    elif 'train_loss' in df.columns:
        ax.plot(df['epoch'], df['train_loss'], color='#9b59b6', alpha=0.7, linewidth=1.5)
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Training Loss')
        ax.set_title('训练损失', fontweight='bold')
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图形
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"✅ 图形已保存到: {output_file}")
    
    # 显示图形
    if show:
        plt.show()
    
    return fig


def plot_comparison(original_history, enhanced_history, output_file=None, show=True):
    """对比两个训练历史"""
    
    if not os.path.exists(original_history):
        print(f"❌ 原始历史文件不存在: {original_history}")
        return
    
    if not os.path.exists(enhanced_history):
        print(f"❌ 增强历史文件不存在: {enhanced_history}")
        return
    
    # 读取数据
    df_orig = pd.read_csv(original_history)
    df_enh = pd.read_csv(enhanced_history)
    
    # 创建图形
    fig, axes = plt.subplots(2, 3, figsize=(18, 10))
    fig.suptitle('训练对比：原始 vs 增强', fontsize=16, fontweight='bold')
    
    metrics = [
        ('ci', 'CI (Concordance Index)', '越高越好'),
        ('mae', 'MAE (Mean Absolute Error)', '越低越好'),
        ('rmse', 'RMSE', '越低越好'),
        ('pearson', 'Pearson Correlation', '越高越好'),
        ('spearman', 'Spearman Correlation', '越高越好'),
        ('mse', 'MSE', '越低越好')
    ]
    
    for idx, (metric, ylabel, desc) in enumerate(metrics):
        row = idx // 3
        col = idx % 3
        ax = axes[row, col]
        
        # 绘制原始版本
        if metric in df_orig.columns:
            ax.plot(df_orig['epoch'], df_orig[metric], 
                   color='#3498db', alpha=0.7, linewidth=2, label='原始版本')
        
        # 绘制增强版本
        if metric in df_enh.columns:
            ax.plot(df_enh['epoch'], df_enh[metric], 
                   color='#e74c3c', alpha=0.7, linewidth=2, label='增强版本')
        
        ax.set_xlabel('Epoch')
        ax.set_ylabel(ylabel)
        ax.set_title(f'{ylabel} - {desc}', fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.legend()
    
    plt.tight_layout()
    
    # 保存图形
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"✅ 对比图已保存到: {output_file}")
    
    # 显示图形
    if show:
        plt.show()
    
    return fig


def print_statistics(history_file):
    """打印训练统计信息"""
    
    if not os.path.exists(history_file):
        print(f"❌ 文件不存在: {history_file}")
        return
    
    df = pd.read_csv(history_file)
    
    print(f"\n{'='*70}")
    print(f"  训练统计 - {os.path.basename(history_file)}")
    print(f"{'='*70}")
    
    print(f"\n总训练轮数: {len(df)}")
    
    if 'is_best' in df.columns:
        best_epochs = df[df['is_best'] == True]
        print(f"最佳轮数: {len(best_epochs)}")
        if len(best_epochs) > 0:
            print(f"首次最佳: Epoch {int(best_epochs.iloc[0]['epoch'])}")
            print(f"最后最佳: Epoch {int(best_epochs.iloc[-1]['epoch'])}")
    
    print(f"\n指标统计:")
    print(f"{'-'*70}")
    
    metrics = ['ci', 'mae', 'rmse', 'pearson', 'spearman']
    for metric in metrics:
        if metric in df.columns:
            values = df[metric].values
            print(f"\n{metric.upper()}:")
            print(f"  最小值: {np.min(values):.6f}")
            print(f"  最大值: {np.max(values):.6f}")
            print(f"  平均值: {np.mean(values):.6f}")
            print(f"  标准差: {np.std(values):.6f}")
            print(f"  最终值: {values[-1]:.6f}")
    
    # 收敛分析
    if 'ci' in df.columns and len(df) >= 50:
        recent_ci = df['ci'].values[-50:]
        print(f"\n收敛分析 (最近50轮):")
        print(f"{'-'*70}")
        print(f"CI标准差: {np.std(recent_ci):.6f}")
        if np.std(recent_ci) < 0.002:
            print("状态: ✅ 已收敛")
        elif np.std(recent_ci) < 0.005:
            print("状态: ⚠️  接近收敛")
        else:
            print("状态: 🔄 仍在优化中")
    
    print(f"\n{'='*70}\n")


def main():
    """主函数"""
    
    print("=" * 70)
    print("  📊 训练可视化工具")
    print("=" * 70)
    
    # 检查文件
    original_history = "outputs/history_davis.csv"
    enhanced_history = "outputs_enhanced/history_davis.csv"
    
    if len(sys.argv) > 1:
        action = sys.argv[1]
        
        if action == 'original':
            # 只显示原始版本
            print("\n📈 绘制原始版本训练曲线...")
            plot_training_curves(original_history, 'training_curves_original.png', show=False)
            print_statistics(original_history)
            
        elif action == 'enhanced':
            # 只显示增强版本
            print("\n📈 绘制增强版本训练曲线...")
            plot_training_curves(enhanced_history, 'training_curves_enhanced.png', show=False)
            print_statistics(enhanced_history)
            
        elif action == 'compare':
            # 对比两个版本
            print("\n📊 绘制对比图...")
            plot_comparison(original_history, enhanced_history, 'training_comparison.png', show=False)
            print("\n原始版本统计:")
            print_statistics(original_history)
            print("\n增强版本统计:")
            print_statistics(enhanced_history)
            
        elif action == 'stats':
            # 只打印统计信息
            if os.path.exists(original_history):
                print_statistics(original_history)
            if os.path.exists(enhanced_history):
                print_statistics(enhanced_history)
        
        else:
            print(f"\n❌ 未知操作: {action}")
            print("\n用法:")
            print("  python visualize_training.py original   # 显示原始版本")
            print("  python visualize_training.py enhanced   # 显示增强版本")
            print("  python visualize_training.py compare    # 对比两个版本")
            print("  python visualize_training.py stats      # 只显示统计信息")
    
    else:
        # 默认：自动检测并显示可用的图表
        if os.path.exists(original_history) and os.path.exists(enhanced_history):
            print("\n✅ 检测到两个版本的训练历史")
            print("📊 生成对比图...")
            plot_comparison(original_history, enhanced_history, 'training_comparison.png', show=False)
            print("\n原始版本统计:")
            print_statistics(original_history)
            print("\n增强版本统计:")
            print_statistics(enhanced_history)
            
        elif os.path.exists(original_history):
            print("\n✅ 检测到原始版本训练历史")
            print("📈 生成训练曲线...")
            plot_training_curves(original_history, 'training_curves_original.png', show=False)
            print_statistics(original_history)
            
        elif os.path.exists(enhanced_history):
            print("\n✅ 检测到增强版本训练历史")
            print("📈 生成训练曲线...")
            plot_training_curves(enhanced_history, 'training_curves_enhanced.png', show=False)
            print_statistics(enhanced_history)
            
        else:
            print("\n❌ 未找到训练历史文件")
            print("请先运行训练：")
            print("  - 原始版本: python main.py")
            print("  - 增强版本: python train_enhanced_v2.py")


if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"\n❌ 错误: {str(e)}")
        import traceback
        traceback.print_exc()

